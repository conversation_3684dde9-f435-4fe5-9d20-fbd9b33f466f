{"name": "my-app", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "dev:mobile": "next dev -H 0.0.0.0", "build": "next build --turbopack", "start": "next start", "lint": "next lint"}, "dependencies": {"@hookform/resolvers": "^5.2.2", "@next/env": "^15.5.3", "@next/third-parties": "^15.5.3", "@portabletext/react": "^4.0.3", "@react-email/components": "^0.5.3", "@sanity/asset-utils": "^2.3.0", "@sanity/client": "^7.11.1", "@sanity/code-input": "^6.0.1", "@sanity/dashboard": "^5.0.0", "@sanity/demo": "^2.0.0", "@sanity/icons": "^3.7.4", "@sanity/image-url": "^1.2.0", "@sanity/ui": "^3.1.0", "@sanity/vision": "^4.9.0", "@t3-oss/env-nextjs": "^0.13.8", "@tabler/icons-react": "^3.35.0", "@tailwindcss/postcss": "^4.1.13", "@uidotdev/usehooks": "^2.4.1", "clsx": "^2.1.1", "css-loader": "^7.1.2", "dotenv": "^17.2.2", "framer-motion": "^12.23.13", "mapbox-gl": "^3.15.0", "motion": "^12.23.13", "next": "^15.5.3", "next-sanity": "^9.12.0", "react": "^19.1.1", "react-dom": "^19.1.1", "react-hook-form": "^7.62.0", "react-scroll-parallax": "^3.4.5", "resend": "^6.1.0", "sanity": "^3.91.0", "sanity-plugin-asset-source-unsplash": "^4.0.1", "sanity-plugin-media": "^4.0.0", "styled-components": "^6.1.19", "suspend-react": "^0.1.3", "tailwind-merge": "^3.3.1", "zod": "^3.24.2"}, "devDependencies": {"@eslint/eslintrc": "^3.3.1", "@svgr/webpack": "^8.1.0", "@tailwindcss/forms": "^0.5.10", "@tailwindcss/typography": "^0.5.16", "@types/node": "^24.5.0", "@types/react": "^19.1.13", "@types/react-dom": "^19.1.9", "@typescript-eslint/eslint-plugin": "^8.44.0", "@typescript-eslint/parser": "^8.44.0", "autoprefixer": "^10.4.21", "eslint": "^9.35.0", "eslint-config-next": "^15.5.3", "postcss": "^8.5.6", "sass": "^1.92.1", "tailwindcss": "^3.4.17", "typescript": "^5.9.2"}}