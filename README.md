## Installing Node

We need a file at ~./bashrc. This is a bash script that will run whenever a new bash terminal is opened. For now we just want to make sure the file exists.

```bash
# Create a bash startup file
echo "" > ~/.bashrc
```

Install Node Version Manager

```bash
# Retrieve and run a bash script that sets up NVM
curl -o- https://raw.githubusercontent.com/nvm-sh/nvm/v0.39.7/install.sh | bash
```

Install the most recent version of node

```bash
# Install the most recent node release
nvm install node
```

NVM and Node are our only global dependencies. Every other dependency for the project will exist within the project folder within `PeopleAndPetsCo/node_modules`

## Cloning the project

```bash
# Navigate to where you want the project folder to live locally
cd <parent folder>

# Clone the repository
git clone https://github.com/LydersGroup/PeopleAndPetsCo.git

# Navigate into the project directory
cd PeopleAndPetsCo
```

Our project dependencies are listed in `./package.json`. They can be installed within the project folder by running

```bash
# Install project dependencies
npm install
```

From here the project should be set up. To run the development server, run the following command:

```bash
# Run the dev server @ localhost:3000
npm run dev
```

Alternatively, we can compile and then run a production server with the following

```bash
# Compile the files
npm run build

# Run the server @ localhost:3000
npm run start
```
