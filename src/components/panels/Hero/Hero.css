#hero li {
  position: relative;
  opacity: 0.7;
}

#hero li:hover {
  opacity: 1;
}

#hero li::after {
  content: "";
  height: 1.5px;
  width: 0;
  background-color: var(--Accent-Color);
  position: absolute;
  left: 0;
  bottom: -2px;
  transition: 0.5s ease-in-out;
  pointer-events: none; /* prevents click indicator pointer when hovering over underline */
}

#hero li:hover::after {
  width: 100%;
}
