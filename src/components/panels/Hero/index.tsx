import Image from "next/image";
import Link from "next/link";
import Logo from "@/images/logo.svg";
import Tech from "@/images/technology.png";
import Plane from "@/images/aviation-w-text-2.png";
import Hardware from "@/images/hardware.png";
import Medical from "@/images/medical.png";
import Farm from "@/images/farm.png";
import Food from "@/images/food-w-text-2.png";
import Science from "@/images/science-w-text.png";
import { useState } from "react";
import handleScroll from "../../../utils/ScrollHandler";
import "./Hero.css";
import {
  motion,
  useMotionValueEvent,
  useScroll,
  useTransform,
  useSpring,
} from "framer-motion";

export default function Hero() {
  const { scrollY } = useScroll();
  const [isAtLimit, setIsAtLimit] = useState(false);
  const fontSize = useTransform(scrollY, [100, 600], ["16vw", "1.5vw"]);
  const xPosition = useTransform(scrollY, [100, 600], ["0vw", "-42vw"]);
  const physics = { mass: 0.27, stiffness: 80 };
  const smoothFontTransition = useSpring(fontSize, physics);
  const smoothXTransition = useSpring(xPosition, physics);

  useMotionValueEvent(scrollY, "change", (value) => {
    if (value > 700) {
      setIsAtLimit(true);
      if (value > 6600) {
        setIsAtLimit(false);
      } else {
        setIsAtLimit(true);
      }
    } else {
      setIsAtLimit(false);
    }
  });

  return (
    <section className="w-full relative" id="hero">
      <div className="relative mx-auto w-full h-screen max-w-[1600px] min-w-[360px] min-h-[1000px] lg:min-h-[900px]">
        <motion.nav
          initial={{ y: -25, opacity: 0 }}
          animate={{ y: 0, opacity: 1 }}
          transition={{ duration: 0.2 }}
          className="absolute top-0 left-0 right-0 z-10"
        >
          <ul className="hidden lg:flex justify-center gap-20 pt-5 text-[14px]">
            <li>
              <Link onClick={handleScroll} href="#whatWeDo">
                What We Do
              </Link>
            </li>
            <li>
              <Link onClick={handleScroll} href="#story">
                Our Story
              </Link>
            </li>
            <li>
              <Link onClick={handleScroll} href="#values">
                Our Values
              </Link>
            </li>
            <li>
              <Link onClick={handleScroll} href="#charities">
                Charities
              </Link>
            </li>
            <li>
              <Link onClick={handleScroll} href="#careers">
                Careers
              </Link>
            </li>
            <li>
              <Link onClick={handleScroll} href="#contact">
                Contact Us
              </Link>
            </li>
          </ul>
        </motion.nav>
        <div>
          <div
            id="hero-copy"
            className="text-center w-[90vw] absolute top-[50%] left-[50%] translate-x-[-50%] translate-y-[-50%]"
          >
            <div className="relative">
              <div className="flex flex-col items-center">
                <Image
                  src={Logo}
                  alt="Lyders Group Logo"
                  className="w-52 md:w-72 lg:w-85 h-auto"
                ></Image>
                <motion.h2
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  transition={{ duration: 1.5, ease: "easeInOut" }}
                  className="text-[5vw] sm:text-[36px]  overflow-hidden  max-w-6xl mx-auto leading-snug mb-4"
                >
                  Building Brighter Futures, <br></br> One Brand at a Time
                </motion.h2>
                <motion.p
                  initial={{ y: 25, opacity: 0 }}
                  animate={{ y: 0, opacity: 1 }}
                  transition={{ duration: 0.5, ease: "easeInOut", delay: 0.5 }}
                  className="text-[14px] sm:text-[16px] md:text-[18px] max-w-xl mx-auto leading-normal"
                >
                  Lyder&apos;s Groups diverse family of businesses touches
                  millions of lives each week, making everyday experiences
                  richer, healthier, and more connected.
                </motion.p>
              </div>
            </div>
          </div>
          <img src={Tech.src} alt="tech symbol" className="tech"></img>
          <img src={Plane.src} alt="aviation" className="aviation"></img>
          <img src={Hardware.src} alt="hardware" className="hardware"></img>
          <img src={Medical.src} alt="medical" className="medical"></img>
          <img src={Farm.src} alt="farm" className="farm"></img>
          <img src={Food.src} alt="food" className="food"></img>
          <img src={Science.src} alt="science" className="science"></img>
        </div>
      </div>
    </section>
  );
}
