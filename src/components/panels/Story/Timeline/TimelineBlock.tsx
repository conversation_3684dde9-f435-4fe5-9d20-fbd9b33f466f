// This component creates the blocks that house the content for the timeline
// Each block on the timeline is split into two more blocks: top and bottom
// The placement of the content alternates between top and bottom to look like
// it's above or below the timeline

export default function TimelineBlock({ placement, year, description }) {
  if (placement === "top") {
    return (
      <div className="w-[350px] h-auto grid grid-rows-2">
        <div className="border-b-2 border-accent-color flex place-items-end pb-8">
          <h3 className="-rotate-90 h-fit pl-6 text-[56px]">{year}</h3>
          <p className="text-[18px]">{description}</p>
        </div>
        <div></div>
      </div>
    );
  }

  return (
    <div className="w-[350px] h-[400px] grid grid-rows-2">
      <div className="border-b-2 border-accent-color"></div>
      <div className="flex pt-8">
        <h3 className="-rotate-90 h-fit pr-6 text-[56px]">{year}</h3>
        <p className="text-[18px]">{description}</p>
      </div>
    </div>
  );
}
