import TimelineBlock from "./TimelineBlock";
import { useRef, useEffect, useState } from "react";
import { useMeasure } from "@uidotdev/usehooks"; // measures and tracks component dimensions
import {
  motion,
  useMotionValue,
  animate,
  useDragControls,
} from "framer-motion";

const timelineContent = [
  {
    year: "1954",
    description: "Danish Import was founded by Lyders Senior",
    placement: "top",
  },
  {
    year: "1985",
    description: "Petersen Products was founded by <PERSON>",
    placement: "bottom",
  },
  {
    year: "1995",
    description: "<PERSON> purchases his first Taco Bell",
    placement: "top",
  },
  {
    year: "1997",
    description: "<PERSON> expandes to multiple location of Taco Bell",
    placement: "bottom",
  },
  {
    year: "2001",
    description: "<PERSON> expands Taco Bell locations out of state",
    placement: "top",
  },
  {
    year: "2005",
    description: "Purchase of Hawk Hollow Farm",
    placement: "bottom",
  },
  {
    year: "2015",
    description:
      "Danish Import and Petersen merge under new name Rockhard Tools",
    placement: "top",
  },
  {
    year: "2020",
    description: "Purchase of Hawk Hollow Aviation",
    placement: "bottom",
  },
  {
    year: "2021",
    description: "Purchase of South Fork Ranch",
    placement: "top",
  },
  {
    year: "2022",
    description: "Purchase of Eberbach Cabinents",
    placement: "bottom",
  },
  {
    year: "2024",
    description: "Purchase of Old West Aviation",
    placement: "top",
  },
  {
    year: "2025",
    description:
      "Opening first location of People and Pets Pharmacy & Rebrand of Rockhard Tools",
    placement: "bottom",
  },
];

export default function Timeline() {
  const [isDragging, setIsDragging] = useState(false);
  const [ref, { width }] = useMeasure(); // measures width of timeline
  const animationControls = useRef(null);
  const xTranslation = useMotionValue(0); //current starting position within the loop
  const dragControls = useDragControls(); //registers Framer dragging feature
  const timelineWidth = width / 2; //width of one iteration of the timeline content
  const speed = width / 100; //constant speed in pixels per second

  // function to start or restart the animation
  function startAnimation() {
    const currentX = xTranslation.get(); //x coordinate of where you left off while dragging
    const widthOfTimeline = -timelineWidth; //end of the loop
    const distance = Math.abs(widthOfTimeline - currentX);
    const duration = distance / speed;

    // 1) continue the animation where it left off while dragging
    animationControls.current = animate(xTranslation, widthOfTimeline, {
      duration,
      ease: "linear",
      onComplete: () => {
        // 2) once full loop is complete, reset animation starting point to 0
        xTranslation.set(0);

        // 3) begin infinite loop
        animationControls.current = animate(xTranslation, widthOfTimeline, {
          duration: width / 2 / speed,
          ease: "linear",
          repeat: Infinity,
          repeatType: "loop",
        });
      },
    });
  }

  // Initialize the animation on mount and when width changes
  useEffect(() => {
    startAnimation();
    return () => animationControls.current?.stop(); // Cleanup on unmount
  }, [width]);

  // Pause animation on hover
  const handleMouseEnter = () => {
    animationControls.current?.pause();
  };

  // Resume animation on mouse leave
  const handleMouseLeave = () => {
    animationControls.current?.play();
  };

  // Function to handle drag events and reset position for infinite scrolling
  const handleDrag = () => {
    animationControls.current?.stop();
    setIsDragging(true);
  };

  // handles the end of the drag interaction
  const handleDragEnd = () => {
    const currentX = xTranslation.get();
    setIsDragging(false);

    // If the timeline is near the end or start, reset it to the beginning
    if (currentX <= -timelineWidth || currentX >= 0) {
      xTranslation.set(currentX % timelineWidth);
    }

    startAnimation();
  };

  return (
    <div
      className="timeline-wrapper overflow-hidden lg:ml-10 mt-32"
      style={{ userSelect: "none" }}
    >
      <motion.div
        className={`flex timeline w-max hover:cursor-grab ${
          isDragging ? "cursor-grabbing" : "cursor-default"
        }`}
        ref={ref}
        style={{ x: xTranslation }}
        onMouseEnter={handleMouseEnter}
        onMouseLeave={handleMouseLeave}
        drag="x" // Enable dragging on the x-axis
        dragControls={dragControls}
        onDrag={handleDrag}
        onDragEnd={handleDragEnd}
      >
        {/* Duplicate and add timeline content for seamless looping */}
        {[...timelineContent, ...timelineContent].map((item, index) => (
          <TimelineBlock
            placement={item.placement}
            year={item.year}
            description={item.description}
            key={index}
          />
        ))}
      </motion.div>
    </div>
  );
}
