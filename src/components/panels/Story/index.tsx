import Timeline from "./Timeline";
import { motion } from "framer-motion";
import Star from "../../Star";
import { AnimatedHeading } from "@/components/ui/AnimatedHeader";

export default function Story() {
  return (
    <section id="story" className="relative mt-40">
      <div>
        <Star height={100} width={100} className="left-96 -top-10" />
        <Star height={200} width={200} className="right-96 top-56" />
        <Star height={80} width={80} className="right-96 top-40" />
        <Star height={60} width={60} className="right-0 bottom-0 left-20" />
        <Star height={50} width={50} className="right-96 bottom-40" />
      </div>
      <div className="w-[100%] px-6 mb-20">
        <AnimatedHeading text={"Get to know us"}></AnimatedHeading>
        <motion.p
          initial={{ y: 25, opacity: 0 }}
          whileInView={{ y: 0, opacity: 1 }}
          transition={{ duration: 1, delay: 0.7 }}
          viewport={{ once: true }}
          className="text-[18px] lg:max-w-[1000px] mb-10 md:mr-6"
        >
          Seventy years ago, the Lyders family set out to build more than just a
          business—he set out to create opportunities, touch lives, and build a
          legacy. Starting with a single hardware business and then a quick
          service restaurant franchise. The Lyder&apos;s entrepreneurial spirit
          and unwavering drive transformed those first two ventures into the
          Lyders Group, now one of the largest family-owned franchise businesses
          in the world.
        </motion.p>
      </div>

      <Timeline></Timeline>
    </section>
  );
}
