import { useEffect, useRef } from "react";
import { motion, useInView } from "framer-motion";
import { HomePagePayload } from "@/types";
import { SanityBlockRenderer } from "@/components/sanity-block-renderer";
import Star from "../../Star";
import { AnimatedHeading } from "@/components/ui/AnimatedHeader";
import Map from "@/images/map-watercolor.png";

interface Props {
  statsSectionTitle: HomePagePayload["statsSectionTitle"];
  statsSectionDescription: HomePagePayload["statsSectionDescription"];
}

// Function to animate statistic values
function animateValue(obj, start, end, duration) {
  let startTimestamp = null;
  const step = (timestamp) => {
    if (!startTimestamp) startTimestamp = timestamp;
    const progress = Math.min((timestamp - startTimestamp) / duration, 1);
    obj.innerHTML = Math.floor(progress * (end - start) + start);
    if (progress < 1) {
      window.requestAnimationFrame(step);
    }
  };
  window.requestAnimationFrame(step);
}

export default function Stats({
  statsSectionTitle,
  statsSectionDescription,
}: Props) {
  const ref1 = useRef(null);
  const ref2 = useRef(null);
  const ref3 = useRef(null);

  const inView1 = useInView(ref1, { once: true });
  const inView2 = useInView(ref2, { once: true });
  const inView3 = useInView(ref3, { once: true });

  //used to apply different animation durations for each statistic to give staggering effect
  useEffect(() => {
    if (inView1) {
      animateValue(ref1.current, 0, 67500, 500);
    }
  }, [inView1]);
  useEffect(() => {
    if (inView2) {
      animateValue(ref2.current, 0, 475000, 1000);
    }
  }, [inView2]);
  useEffect(() => {
    if (inView3) {
      animateValue(ref3.current, 0, 24500000, 1500);
    }
  }, [inView3]);

  return (
    <section className="px-6 -mt-40 mb-40 lg:mr-6 flex flex-col  relative">
      <div>
        <Star width={65} height={65} className="left-80 top-40"></Star>
        <Star width={120} height={120} className="top-5 right-20"></Star>
        <Star width={350} height={350} className="bottom-0 right-96"></Star>
      </div>
      <AnimatedHeading text={statsSectionTitle}></AnimatedHeading>
      <motion.div
        initial={{ y: 25, opacity: 0 }}
        whileInView={{ y: 0, opacity: 1 }}
        transition={{ duration: 0.5, delay: 0.3 }}
        viewport={{ once: true }}
        className="w-[100%] lg:max-w-[1000px] text-[18px]"
      >
        <SanityBlockRenderer value={statsSectionDescription} />
      </motion.div>
      <div className="flex flex-col lg:flex-row justify-center items-center gap-[40px] w-full max-w-[1400px] mx-auto mt-16 z-[2]">
        <img
          className="mt-8 lg:mt-0 self-center rounded-lg w-full lg:w-[40vw] min-w-[500px]"
          alt="map of michigan with pins"
          src={Map.src}
        ></img>

        <div className="flex flex-col items-center w-full sm:min-w-[300px] sm:max-w-[500px] gap-10 mx-auto mt-10 md:mt-0">
          <div className="flex flex-col items-center justify-center w-[100%] py-8 px-12 border-2 border-main-font rounded-xl bg-white-card shadow-custom">
            <h2
              className="text-[10vw] md:text-[8vw] lg:text-[2.5vw] text-center animate-number"
              ref={ref1}
            >
              67500
            </h2>
            <p className="text-center text-[20px] lg:text-[14px]">
              People served daily
            </p>
          </div>
          <div className="flex flex-col items-center justify-center w-[100%] py-8 px-12 border-2 border-main-font rounded-xl bg-white-card shadow-custom">
            <h2
              className="text-[10vw] md:text-[8vw] lg:text-[2.5vw] text-center animate-number"
              ref={ref2}
            >
              475000
            </h2>
            <p className="text-center text-[20px] lg:text-[14px]">
              People served weekly
            </p>
          </div>
          <div className="flex flex-col items-center justify-center w-[100%] py-8 px-12 border-2 border-main-font rounded-xl bg-white-card shadow-custom">
            <h2
              className="text-[10vw] md:text-[8vw] lg:text-[2.5vw] text-center animate-number"
              ref={ref3}
            >
              24500000
            </h2>
            <p className="text-center text-[20px] lg:text-[14px]">
              People served annually
            </p>
          </div>
        </div>
      </div>
    </section>
  );
}
