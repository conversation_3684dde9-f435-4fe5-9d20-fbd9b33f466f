import Image from "next/image";
import LinkedIn from "../../../../public/images/linkedin.png";
import Instagram from "../../../../public/images/instagram.png";
import Facebook from "../../../../public/images/facebook.png";
import Arrow from "../../../../public/images/up-arrow.png";
import handleScroll from "../../../utils/ScrollHandler";
import Star from "@/images/star.svg";
import { motion } from "framer-motion";
import Link from "next/link";

export default function Footer() {
  return (
    <div className="md:pb-1">
      <footer className=" pt-10 pb-5 bg-[#0A2749] md:m-6 rounded-t-3xl md:rounded-b-3xl flex flex-col text-white-font overflow-hidden relative">
        <Image
          src={Star}
          alt="compass"
          className="absolute opacity-5 h-[900px] w-[900px] right-0"
        ></Image>
        {/* Desktop Grid */}
        <div className="hidden sm:grid grid-cols-3 text-center px-3 sm:px-[15%] md:px-[20%] text-[18px] lg:text-[14px] text-white-font text-opacity-90">
          {/* Column 1: Site Map + Socials */}
          <div className="mb-10">
            <h3 className="mb-3">Site Map</h3>
            <ul className="text-[14px] flex flex-col gap-3">
              <li>
                <a onClick={handleScroll} href="#whatWeDo">
                  What We Do
                </a>
              </li>
              <li>
                <a onClick={handleScroll} href="#story">
                  Our Story
                </a>
              </li>
              <li>
                <a onClick={handleScroll} href="#values">
                  Our Values
                </a>
              </li>
              <li>
                <a onClick={handleScroll} href="#charities">
                  Charities
                </a>
              </li>
              <li>
                <a onClick={handleScroll} href="#contact">
                  Contact Us
                </a>
              </li>
              <li>
                <a onClick={handleScroll} href="#careers">
                  Careers
                </a>
              </li>
            </ul>
            <div className="mt-10">
              <h3 className="mb-3">Socials</h3>
              <ul className="flex gap-2 justify-center">
                <li>
                  <Image src={LinkedIn} className="w-6 h-6" alt="LinkedIn" />
                </li>
                <li>
                  <Image src={Instagram} className="w-6 h-6" alt="Instagram" />
                </li>
                <li>
                  <Image src={Facebook} className="w-6 h-6" alt="Facebook" />
                </li>
              </ul>
            </div>
          </div>

          {/* Column 2: Our Projects */}
          <div className="mb-10">
            <h3 className="mb-3">Our Projects</h3>
            <ul className="text-[14px] flex flex-col gap-3">
              <li>
                <Link href="https://www.teamlyders.com/" target="_blank">
                  Team Lyders
                </Link>
              </li>
              <li>
                <Link href="https://www.rockhardtoolsinc.com/" target="_blank">
                  Rockhard Tools
                </Link>
              </li>
              <li>
                <Link href="https://eberbachcabinets.com/" target="_blank">
                  Eberbach Cabinets
                </Link>
              </li>
            </ul>
          </div>

          {/* Column 3: Head Office, Phone, Email */}
          <div>
            <h3 className="mb-3">Head Office</h3>
            <p className="text-[14px]">7915 Kensington Ct</p>
            <p className="text-[14px]">Brighton, MI 48116</p>

            <h3 className="mt-6 mb-3">Phone</h3>
            <p className="text-[14px]">************</p>

            <h3 className="mt-6 mb-3">Email</h3>
            <ul className="text-[14px] flex flex-col gap-3">
              <li><EMAIL></li>
            </ul>
          </div>
        </div>

        {/* Mobile Grid */}
        <div className="grid sm:hidden grid-cols-2 text-center px-3 text-[18px] lg:text-[14px] text-white-font text-opacity-90">
          {/* Column 1: Site Map + Our Projects */}
          <div className="mb-10">
            <h3 className="mb-3">Site Map</h3>
            <ul className="text-[14px] flex flex-col gap-3">
              <li>
                <a onClick={handleScroll} href="#whatWeDo">
                  What We Do
                </a>
              </li>
              <li>
                <a onClick={handleScroll} href="#story">
                  Our Story
                </a>
              </li>
              <li>
                <a onClick={handleScroll} href="#values">
                  Our Values
                </a>
              </li>
              <li>
                <a onClick={handleScroll} href="#charities">
                  Charities
                </a>
              </li>
              <li>
                <a onClick={handleScroll} href="#contact">
                  Contact Us
                </a>
              </li>
              <li>
                <a onClick={handleScroll} href="#careers">
                  Careers
                </a>
              </li>
            </ul>

            <h3 className="mt-10 mb-3">Our Projects</h3>
            <ul className="text-[14px] flex flex-col gap-3">
              <li>
                <a href="https://www.teamlyders.com/" target="_blank">
                  Team Lyders
                </a>
              </li>
              <li>
                <a href="https://www.rockhardtoolsinc.com/" target="_blank">
                  Rockhard Tools
                </a>
              </li>
              <li>
                <a href="https://eberbachcabinets.com/" target="_blank">
                  Eberbach Cabinets
                </a>
              </li>
            </ul>
          </div>

          {/* Column 2: Head Office, Phone, Email, Socials */}
          <div>
            <h3 className="mb-3">Head Office</h3>
            <p className="text-[14px]">7915 Kensington Ct</p>
            <p className="text-[14px]">Brighton, MI 48116</p>

            <h3 className="mt-6 mb-3">Phone</h3>
            <p className="text-[14px]">************</p>

            <h3 className="mt-6 mb-3">Email</h3>
            <ul className="text-[14px] flex flex-col gap-3">
              <li><EMAIL></li>
            </ul>

            <div className="mt-10">
              <h3 className="mb-3">Socials</h3>
              <ul className="flex gap-2 justify-center">
                <li>
                  <Image src={LinkedIn} className="w-6 h-6" alt="LinkedIn" />
                </li>
                <li>
                  <Image src={Instagram} className="w-6 h-6" alt="Instagram" />
                </li>
                <li>
                  <Image src={Facebook} className="w-6 h-6" alt="Facebook" />
                </li>
              </ul>
            </div>
          </div>
        </div>

        <div className="text-[15vw] text-center flex flex-nowrap justify-center overflow-hidden">
          <motion.span
            initial={{ y: 25, opacity: 0 }}
            whileInView={{ y: 0, opacity: 1 }}
            transition={{ duration: 0.1 }}
            viewport={{ amount: 0.2, once: true }}
            className="inline-block"
          >
            L
          </motion.span>
          <motion.span
            initial={{ y: 25, opacity: 0 }}
            whileInView={{ y: 0, opacity: 1 }}
            transition={{ duration: 0.1, delay: 0.1 }}
            viewport={{ amount: 0.2, once: true }}
            className="inline-block"
          >
            y
          </motion.span>
          <motion.span
            initial={{ y: 25, opacity: 0 }}
            whileInView={{ y: 0, opacity: 1 }}
            transition={{ duration: 0.1, delay: 0.2 }}
            viewport={{ amount: 0.2, once: true }}
            className="inline-block"
          >
            d
          </motion.span>
          <motion.span
            initial={{ y: 25, opacity: 0 }}
            whileInView={{ y: 0, opacity: 1 }}
            transition={{ duration: 0.1, delay: 0.3 }}
            viewport={{ amount: 0.2, once: true }}
            className="inline-block"
          >
            e
          </motion.span>
          <motion.span
            initial={{ y: 25, opacity: 0 }}
            whileInView={{ y: 0, opacity: 1 }}
            transition={{ duration: 0.1, delay: 0.4 }}
            viewport={{ amount: 0.2, once: true }}
            className="inline-block"
          >
            r
          </motion.span>
          <motion.span
            initial={{ y: 25, opacity: 0 }}
            whileInView={{ y: 0, opacity: 1 }}
            transition={{ duration: 0.1, delay: 0.5 }}
            viewport={{ amount: 0.2, once: true }}
            className="inline-block"
          >
            s
          </motion.span>
          <motion.span className="inline-block">&nbsp;</motion.span>
          <motion.span
            initial={{ y: 25, opacity: 0 }}
            whileInView={{ y: 0, opacity: 1 }}
            transition={{ duration: 0.1, delay: 0.6 }}
            viewport={{ amount: 0.2, once: true }}
            className="inline-block"
          >
            G
          </motion.span>
          <motion.span
            initial={{ y: 25, opacity: 0 }}
            whileInView={{ y: 0, opacity: 1 }}
            transition={{ duration: 0.1, delay: 0.7 }}
            viewport={{ amount: 0.2, once: true }}
            className="inline-block"
          >
            r
          </motion.span>
          <motion.span
            initial={{ y: 25, opacity: 0 }}
            whileInView={{ y: 0, opacity: 1 }}
            transition={{ duration: 0.1, delay: 0.8 }}
            viewport={{ amount: 0.2, once: true }}
            className="inline-block"
          >
            o
          </motion.span>
          <motion.span
            initial={{ y: 25, opacity: 0 }}
            whileInView={{ y: 0, opacity: 1 }}
            transition={{ duration: 0.1, delay: 0.9 }}
            viewport={{ amount: 0.2, once: true }}
            className="inline-block"
          >
            u
          </motion.span>
          <motion.span
            initial={{ y: 25, opacity: 0 }}
            whileInView={{ y: 0, opacity: 1 }}
            transition={{ duration: 0.1, delay: 1 }}
            viewport={{ amount: 0.2, once: true }}
            className="inline-block"
          >
            p
          </motion.span>
        </div>

        <div className="flex justify-between items-center px-10">
          <h3 className="text-[12px] text-white-font text-opacity-80">
            Copyright &copy; Lyders Group
          </h3>
          <Link
            className="flex gap-2 items-center z-50"
            onClick={handleScroll}
            href="#hero"
          >
            Back to top
            <Image src={Arrow} className="h-5 w-5" alt="Arrow" />
          </Link>
        </div>
      </footer>
    </div>
  );
}
