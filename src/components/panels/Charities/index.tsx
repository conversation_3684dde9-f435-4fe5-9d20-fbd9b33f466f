import "./Charities.css";
import Image from "next/image";
import Dog from "../../../../public/images/dog.png";
import Cat from "../../../../public/images/cat.png";
import WalkingDog from "../../../../public/images/walking-dog.png";
import DogHouse from "../../../../public/images/doghouse.png";
import Heart from "../../../../public/images/heart.png";
import Hand from "../../../../public/images/hand.png";
import Star from "../../Star";
import { motion } from "framer-motion";
import { AnimatedHeading } from "@/components/ui/AnimatedHeader";

export default function Charities() {
  return (
    <section
      className="my-40 md:px-6 charities relative lg:mr-6"
      id="charities"
    >
      <div>
        <Star height={100} width={100} className="top-20 left-96" />
        <Star height={50} width={50} className="-top-20 left-[800px]" />
        <Star height={400} width={400} className="top-10 -right-40" />
        <Star height={200} width={200} className="-bottom-40 left-10" />
        <Star height={80} width={80} className="-bottom-40 left-[800px]" />
        <Star height={100} width={100} className="-bottom-56 left-[1000px]" />
      </div>
      <div className="px-6 md:px-0 ">
        <AnimatedHeading text={"Charities"}></AnimatedHeading>
        <p className="w-[100%] lg:max-w-[1000px] text-[18px] mb-10">
          Lyders Group is driven by a mission to give back to the communities it
          serves, supporting local charities, animal welfare, and families in
          need across the country.
        </p>
      </div>
      <div className="bento">
        <motion.article
          initial={{ opacity: 0, y: -25 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3 }}
          viewport={{ once: true }}
          className="bento-item one"
        >
          <div>
            <h3 className="text-[36px]">Adopt-a-Family</h3>
            <p>
              Each year, we collaborate with Restaurant Leadership to select an
              employee at each restaurant who may be struggling, providing them
              with a $400 shopping spree to help make their holidays memorable.
              <br></br>
              <br></br>
              Additionally, we work with local organizations to identify
              families in need, donating holiday gifts and essentials to ensure
              they have a full and memorable holiday season.
            </p>
          </div>
          <div className="bottom">
            <div className="partners">
              <h4>OUR PARTNERS</h4>
              <ul>
                <li>LACASA - Howell, MI</li>
                <li>Active Faith - South Lyon, MI</li>
              </ul>
            </div>
            <Image src={Heart} alt="Heart" className="w-12 h-12 icon" />
          </div>
        </motion.article>
        <motion.article
          initial={{ opacity: 0, y: -25 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3 }}
          viewport={{ once: true }}
          className="bento-item two "
        >
          <div>
            <h3 className="text-[36px]">Cascades Humane Society</h3>
            <p>
              Lyders Group partnered with the Cascades Humane Society to connect
              animals in need with people who care. Utilizing all of the
              different businesses to raise money and awareness, Lyders Group is
              helping the community to give all pets loving homes and ensuring
              that all animals are treated with compassion and respect.
            </p>
          </div>
          <Image src={Cat} alt="Cat" className="w-12 h-12 icon" />
        </motion.article>
        <motion.article
          initial={{ opacity: 0, y: -25 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3 }}
          viewport={{ once: true }}
          className="bento-item three "
        >
          <div>
            <h3 className="text-[36px]">Dog House Collection</h3>
            <p>
              We support over 50 Shelters / Rescues, annually, through our
              &quot;Dog House&quot; collection banks in our Restaurants. Funds
              are collected in the Dog House bank, tallied and then donated to a
              local Shelter / Rescue.
              <br></br>
              <br></br>
              At the end of the year, Team Lyders will donate a 100% match of
              what we collected thoughout the year.
            </p>
          </div>
          <Image src={DogHouse} alt="Dog House" className="w-12 h-12 icon" />
        </motion.article>
        <motion.article
          initial={{ opacity: 0, y: -25 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3 }}
          viewport={{ once: true }}
          className="bento-item four "
        >
          <div>
            <h3 className="text-[36px]">Local Donations</h3>
            <p>
              We make numerous, generous, donations of time, money and food to
              local Libraries, Churches, Schools, Cancer Organizations, Social
              Clubs, and many more!
            </p>
          </div>
          <Image src={Hand} alt="Hand with Heart" className="w-12 h-12 icon" />
        </motion.article>
        <motion.article
          initial={{ opacity: 0, y: -25 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3 }}
          viewport={{ once: true }}
          className="bento-item five "
        >
          <div>
            <h3 className="text-[36px]">Capital Area Humane Society</h3>
            <p>
              The Lyders Group has long supported the Capital Humane Society in
              support of animal welfare. Utilizing our resources, we aim to make
              an impact on surrounding areas that need help with their animals
              receiving nutritious food, safe shelter, medical care, and loving
              homes.
            </p>
          </div>
          <Image src={Dog} alt="Dog" className="w-16 h-16 icon" />
        </motion.article>
        <motion.article
          initial={{ opacity: 0, y: -25 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3 }}
          viewport={{ once: true }}
          className="bento-item six "
        >
          <div>
            <h3 className="text-[36px]">Mutt March</h3>
            <p>
              Since 1989, Mutt March has served as the Michigan Humane
              Society&apos;s annual fundraising walk, celebrating people and
              their dogs.
              <br></br> <br></br> Lyders Group is a proud partner of the Mutt
              March initiative and will continue to donate to this amazing
              cause.
            </p>
          </div>
          <Image
            src={WalkingDog}
            alt="Walking Dog"
            className="w-12 h-12 icon"
          />
        </motion.article>
      </div>
    </section>
  );
}
