.bento {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  grid-template-rows: repeat(3, 1fr);
  gap: 16px;
  max-width: 1800px;
  margin: 0 auto;
}

.bento-item {
  padding: 30px;
  border: 2px solid var(--Black-Font);
  border-radius: 20px;
  background-color: var(--White-Card);
  color: var(--Black-Font);
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  box-shadow: 5px 5px 0 #0a2749;
  z-index: 1;
}

.bento-item h3 {
  margin-bottom: 10px;
}

.icon {
  align-self: flex-end;
  height: 60px;
  width: 60px;
}

.bottom {
  display: flex;
  justify-content: space-between;
}

.partners h4 {
  margin-bottom: 10px;
  font-weight: 500;
}

.partners li {
  opacity: 0.8;
}

.bento-item.one {
  grid-row: 1 / 3;
}

.bento-item.two {
  grid-row: 3 / -1;
  grid-column: 1 / 3;
}

.bento-item.three {
  grid-column: 2 / -1;
}

.bento-item.four {
  grid-row: 2 / 3;
  grid-column: 2 / 3;
}

.bento-item.five {
  grid-row: 2 / 3;
  grid-column: 3 / -1;
}

.bento-item.six {
  grid-row: 3 / -1;
  grid-column: 3 / -1;
}

@media (max-width: 1400px) {
  .bento {
    grid-template-columns: 1fr 1fr;
    grid-template-rows: repeat(4, 1fr);
  }

  .bento-item.one {
    grid-row: 1 / 3;
  }

  .bento-item.two {
    grid-row: 4 / -1;
    grid-column: 1 / -1;
  }

  .bento-item.three {
    grid-column: 2 / -1;
  }

  .bento-item.four {
    grid-row: 2 / 3;
    grid-column: 2 / -1;
  }

  .bento-item.five {
    grid-row: 3 / 4;
    grid-column: 2 / -1;
  }

  .bento-item.six {
    grid-row: 3 / 4;
    grid-column: 1 / 2;
  }
}

@media (max-width: 920px) {
  .bento {
    display: flex;
    flex-direction: column;
    align-items: stretch;
  }

  .bento-item {
    height: 450px;
    position: sticky;
    top: 70px;
    padding: 20px;
  }

  .bento-item.two {
    transform: translateY(15px) !important;
  }

  .bento-item.three {
    transform: translateY(30px) !important;
  }

  .bento-item.four {
    transform: translateY(45px) !important;
  }

  .bento-item.five {
    transform: translateY(60px) !important;
  }

  .bento-item.six {
    transform: translateY(75px) !important;
  }
}

@media (max-width: 500px) {
  .bento-item {
    height: 620px;
  }

  .bento-item:first-child {
    height: 620px;
    justify-content: start !important;
    gap: 40px !important;
  }

  .bottom .icon {
    margin-bottom: 40px;
  }

  .icon {
    margin-bottom: 80px;
  }
}
