import Card from "./dnaCard";
import { motion } from "framer-motion";
import { HomePagePayload } from "@/types";
import Star from "../../Star";
import { AnimatedHeading } from "@/components/ui/AnimatedHeader";

const dnaCardContent = [
  {
    title: "Build",
    text: "Build and develop teams that have meaning and purpose.",
  },
  {
    title: "Create",
    text: "Creating opportunities for our teams to positively impact the communities where we work, eat, and play.",
  },
  {
    title: "Define",
    text: "Using a proven and tested strategy to define a structure and create clarity for each team member to be successful",
  },
  {
    title: "Trust",
    text: "Steadfast dependability so our consumers and employees can rely on and trust that we will be there for them.",
  },
  {
    title: "Safety",
    text: "Creating a safe place for consumers and employees where they feel safe both physically and psychologically.",
  },
];

interface Props {
  purposeStatement: HomePagePayload["purposeStatement"];
  ambitionStatement: HomePagePayload["ambitionStatement"];
}

export default function Values({ purposeStatement, ambitionStatement }: Props) {
  return (
    <section
      className="sm:w-[100%] my-32 flex flex-col gap-20 relative"
      id="values"
    >
      <div>
        <Star height={350} width={350} className="right-96 -top-10" />
        <Star height={140} width={140} className="left-96 top-[420px]" />
        <Star height={140} width={140} className="left-20 bottom-0" />
        <Star height={50} width={50} className="left-10 bottom-0" />
        <Star height={200} width={200} className="bottom-20 right-10" />
      </div>
      <div className="mb-16">
        <div className="ml-6">
          <AnimatedHeading text={"How we do it"}></AnimatedHeading>
        </div>

        <div>
          <motion.div
            className="flex overflow-x-auto gap-3 pl-6 pr-3 pb-2 w-[100%]"
            id="dnaCardContainer"
          >
            {dnaCardContent.map((item, index) => {
              return (
                <Card
                  title={item.title}
                  text={item.text}
                  key={index}
                  index={index}
                />
              );
            })}
          </motion.div>
        </div>
      </div>
      <div className="flex flex-col justify-between gap-10  px-6">
        <div className="flex flex-col sm:flex-row justify-center">
          <h2 className="text-[36px] sm:text-[44px] w-[20%] mr-20">Purpose</h2>
          <p className="w-[100%] sm:w-[800px] text-[18px]">
            {purposeStatement}
          </p>
        </div>
        <motion.div
          initial={{ width: 0 }}
          whileInView={{ width: "80%" }}
          transition={{ duration: 1 }}
          viewport={{ once: true, amount: "all" }}
          className="w-[80%] max-w-[850px] border-b-2 border-accent-color self-center "
        ></motion.div>
        <div className="flex flex-col sm:flex-row justify-center">
          <h2 className="text-[36px] sm:text-[44px] w-[20%] mr-20">Ambition</h2>
          <p className="w-[100%] sm:w-[800px] text-[18px]">
            {ambitionStatement}
          </p>
        </div>
      </div>
    </section>
  );
}
