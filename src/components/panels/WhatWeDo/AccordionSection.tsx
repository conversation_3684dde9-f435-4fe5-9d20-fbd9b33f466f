//The cards for each segment in the WhatWeDo Section

import { motion } from "framer-motion";
import Image, { type StaticImageData } from "next/image";
import Arrow from "@/images/arrow.png";
import Tech from "@/images/tech.png";
import Link from "next/link";

type Brand = {
  image: StaticImageData;
  link: string;
};

type Props = {
  SegmentName: string;
  Founded: string;
  Description: string;
  Brands?: Brand[];
  imageSource: StaticImageData;
  Color: string;
  icon: StaticImageData;
  index: number;
  isExpanded: boolean;
  onExpand: () => void;
};

export default function AccordionSection({
  SegmentName,
  Founded,
  Description,
  Brands,
  imageSource,
  Color,
  icon,
  index,
  isExpanded,
  onExpand,
}: Props) {
  // calculates the transform value to move each card up to overlap each other
  const transformValue = 20 + 45 * index;

  return (
    <div
      className={`${
        isExpanded ? "h-fit min-h-[550px]" : "min-h-[160px]"
      } cursor-pointer flex py-5 px-[4vw] sm:px-[5vw] rounded-3xl transition-[min-height] border-main-font duration-700  h-[150px] text-main bg-white-card border-2 text-main-font shadow-custom`}
      style={{
        transform: `translateY(-${transformValue}px)`,
      }}
      onClick={onExpand}
    >
      {/* Card Header */}
      <div className="flex flex-col w-full">
        <div className="flex items-center justify-between">
          <div className="flex items-center">
            <Image src={icon} alt="segment icon" height={48} width={48} />
            <div className="ml-8">
              <h2 className="text-[36px]">{SegmentName}</h2>
              <h3 className="opacity-75">EST. {Founded}</h3>
            </div>
          </div>
          <button
            className={`${
              isExpanded ? "rotate-90" : "rotate-0"
            } transition duration-300`}
          >
            <Image src={Arrow} alt="down arrow" className="h-12 w-auto " />
          </button>
        </div>

        {/* Card Content */}
        <div
          className={`${
            isExpanded ? "opacity-1" : "opacity-0"
          } transition duration-700 px-5 relative`}
        >
          <div className="flex flex-col items-center xl:flex-row xl:gap-6">
            <motion.p className="text-[18px] my-5 self-center lg:self-start lg:flex-1">
              {Description}
            </motion.p>

            {imageSource && (
              <div className="h-80 w-full justify-center -mt-5 xl:mt-0 max-w-[450px] mx-auto lg:mx-0 lg:flex hidden">
                <Image
                  src={imageSource}
                  className={`h-80 w-auto ${
                    SegmentName === "Hardware" ? "xl:p-5 p-10" : null
                  }`}
                  alt=""
                />
              </div>
            )}
          </div>

          {Brands && (
            <div id="brands" className="flex flex-col  mb-16">
              <p className="mb-2 font-medium">OUR BRANDS</p>
              <div className="flex gap-10">
                {Brands.map((brand, index) => (
                  <a target="_blank" href={brand.link} key={index}>
                    <Image
                      src={brand.image}
                      width="200"
                      height="200"
                      alt=""
                    ></Image>
                  </a>
                ))}
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
