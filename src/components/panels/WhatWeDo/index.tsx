import { useState } from "react";
import AccordionSection from "./AccordionSection";
import Tech from "../../../images/tech-symbol.png";
import Aviation from "../../../images/plane.png";
import Medical from "../../../images/medical-symbol.png";
import Hardware from "../../../images/hardware-symbol.png";
import FoodIcon from "../../../images/segment-icons/food.png";
import AviationIcon from "../../../images/segment-icons/aviation.png";
import HardwareIcon from "../../../images/segment-icons/hardware.png";
import MedicalIcon from "../../../images/segment-icons/medical.png";
import TechIcon from "../../../images/segment-icons/tech.png";
import ScientificIcon from "../../../images/segment-icons/science.png";
import FarmIcon from "../../../images/segment-icons/farm.png";
import Star from "../../Star";
import { AnimatedHeading } from "@/components/ui/AnimatedHeader";
import <PERSON><PERSON><PERSON> from "../../../images/segment-logos/eberbach.svg";
import PNP from "../../../images/segment-logos/pnp.png";
import Rockhard from "../../../images/segment-logos/rockhard-tools.svg";
import TeamLyders from "../../../images/segment-logos/team-lyders.svg";
import HawkHollow from "../../../images/segment-logos/hawk-hollow.png";
import Farm from "../../../images/farm-symbol.png";
import Food from "../../../images/food-symbol.png";
import Science from "@/images/science-symbol.png";

const CardValues = [
  {
    SegmentName: "Food",
    Established: "1995",
    Description:
      "Lyders Food is a division of the Lyders Group, dedicated to bringing genuine care and operational excellence to the quick-service restaurant industry. We proudly operate leading brands including Taco Bell and Arby’s, delivering high-quality food and service our guests can count on. With a focus on people, performance, and community, we aim to create welcoming dining experiences and strong local connections. At Lyders Food, we combine the strength of national brands with a people-first approach to help shape the future of quick-service dining.",
    Image: Food,
    Brands: [{ image: TeamLyders, link: "https://www.teamlyders.com/" }],
    Color: "#CD33CB",
    icon: FoodIcon,
  },
  {
    SegmentName: "Technology",
    Established: "2012",
    Description:
      "Lyders Technology powers technology across the entire organization, building infrastructure, securing our physical locations and websites, and developing custom apps. It is leading technological advancements across all divisions and putting Lyders Group in an industry-leading position.",
    Image: Tech,
    Color: "#FC3800",
    icon: TechIcon,
  },
  {
    SegmentName: "Hardware",
    Established: "1954",
    Description:
      "Lyders Hardware, a division of the Lyders Group, is the company’s original business, with a legacy spanning over 70 years. Today, that tradition continues under the Rockhard Tools brand, where we manufacture, supply, and retail high-quality tools and hardware to professionals across the industry. Built on decades of expertise and trust, Lyders Hardware remains committed to delivering durable products and dependable service—supporting tradespeople and businesses with the tools they need to get the job done right.",

    Image: Hardware,
    Brands: [{ image: Rockhard, link: "" }],
    Color: "#FFC152",
    icon: HardwareIcon,
  },
  {
    SegmentName: "Medical",
    Established: "2025",
    Description:
      "Lyders Medical, a division of Lyders Group is the home of the newest breed of pharmacies, People and Pets Pharmacy.  We are commercializing a new way to shop for medication.  Through new technologies and a genuine care to make our community a healther place, we are entering the medical industry looking to disrupt how people and pets get their prescriptions.",
    Image: Medical,
    Brands: [{ image: PNP, link: "https://www.peopleandpetspharmacy.com/" }],
    Color: "#00BEE3",
    icon: MedicalIcon,
  },
  {
    SegmentName: "Aviation",
    Established: "2020",
    Description:
      "Lyders Aviation, a division of Lyders Group, was formed as a passion project but later became a necessity as a way to move more pieces of the businesses around quickly and efficiently.",
    Image: Aviation,
    Color: "#DDA62C",
    icon: AviationIcon,
  },
  {
    SegmentName: "Scientific",
    Established: "2022",
    Description:
      "Lyders Scientific is a division of Lyders Group and is proud to serve the scientific research community.  Our brand, Eberbach Cabinents and products offer something truly niche and look forward to supporting teams all over the world as they solve the worlds most pressing problems.",
    Brands: [{ image: Eberbach, link: "https://www.eberbachcabinets.com/" }],
    Image: Science,
    Color: "#08D2B6",
    icon: ScientificIcon,
  },
  {
    SegmentName: "Farm",
    Established: "2005",
    Description:
      "Lyders Farm, a division of the Lyders Group, is our most unique and beloved venture—a fully operational ranch and farm that’s home to over 150 wild and exotic animal breeds. Blending agriculture, education, and conservation, Lyders Farm offers a one-of-a-kind environment where care for animals and connection to nature are at the heart of everything we do.",

    Image: Farm,
    Brands: [{ image: HawkHollow, link: "" }],
    Color: "#D2EF00",
    icon: FarmIcon,
  },
];

export default function WhatWeDo() {
  const [expandedIndex, setExpandedIndex] = useState<number | null>(null);

  // Close accordion section if already open
  const toggleSection = (index: number) => {
    setExpandedIndex(expandedIndex === index ? null : index);
  };

  return (
    <section className="md:px-6 my-16 relative lg:mr-6 " id="whatWeDo">
      <div>
        <Star width={50} height={50} className="left-12" />
        <Star width={80} height={80} className="top-5 left-20" />
        <Star width={120} height={120} className="top-5 right-20" />
        <Star width={180} height={180} className="bottom-40 left-32" />
        <Star width={180} height={180} className="right-32 top-48" />
      </div>
      <div className="w-[100%] lg:max-w-[1000px] text-main-font py-16 px-6 -mb-12">
        <div className="mb-3 text-[48px]">
          <AnimatedHeading text={"What we do"}></AnimatedHeading>
        </div>
        <div className="flex flex-col text-main-font gap-5 text-[18px]">
          <p>
            What began as one restaurant and a tool business has grown into an
            expansive portfolio of brands divided into seven operating segments:
            Lyders Food, Lyders Farms, Lyders Aviation, Lyders Hardware, Lyders
            Technology, Lyders Scientific, and Lyders Medical.
          </p>
          <p>
            Today, Lyders Group operates over 240 Taco Bell and Arby’s
            franchises, two farms that connect us to the land, an aviation
            hangar that takes us to new heights, a pharmacy and medical cabinets
            that support health, wellness, and research, and a tool company that
            helps build the future. Through these diverse enterprises, we serve
            millions of people every week, making their lives a little brighter,
            easier, and more fulfilling.
          </p>
        </div>
      </div>

      <div className="w-full max-w-[1600px] mt-10 text-white-font mx-auto">
        {CardValues.map((item, index) => (
          <AccordionSection
            SegmentName={item.SegmentName}
            Founded={item.Established}
            Description={item.Description}
            Brands={item.Brands}
            imageSource={item.Image}
            Color={item.Color}
            icon={item.icon}
            key={index}
            isExpanded={expandedIndex === index}
            onExpand={() => toggleSection(index)}
            index={index}
          />
        ))}
      </div>
    </section>
  );
}
