import { useForm } from "react-hook-form";
import { z } from "zod";
import { zodResolver } from "@hookform/resolvers/zod";
import { useState } from "react";
import Star from "@/components/Star";

//handles phone number format options
//allows for an optional international code (with or without a +), optional parentheses around area codes, and spaces or dashes as separators between parts of the phone number
const phoneRegex = new RegExp(
  /^(?:\+?\d{1,4})?[\s\-]?\(?\d{1,4}\)?[\s\-]?\d{1,4}[\s\-]?\d{1,4}$/
);

const formSchema = z.object({
  firstName: z.string().min(1, "First name is required"),
  lastName: z.string(),
  email: z.string().email().min(1, "Email is required"),
  phone: z.string().regex(phoneRegex, "Invalid phone number"), //DEV NOTE: update to not require phone number
  message: z.string().min(1, "Message is required"),
  honeypot: z.string().optional(), //used to prevent spam bots from submitting the form
});

//generates a typescript type based on the schema
type FormValues = z.infer<typeof formSchema>;

export default function Contact() {
  const [SuccessMessage, SetSuccessMessage] = useState("Send Message");
  const [error, setError] = useState("");
  const {
    register,
    handleSubmit,
    reset,
    formState: { errors, isSubmitting },
  } = useForm<FormValues>({
    resolver: zodResolver(formSchema), //use zod for validation based on schema
  });

  //send form data to contact api route
  const onSubmit = async (values: FormValues) => {
    const res = await fetch("/api/send-email", {
      method: "POST",
      headers: { "Content-Type": "application/json" },
      body: JSON.stringify({
        Source: "LydersGroup",
        FirstName: values.firstName,
        LastName: values.lastName,
        Email: values.email,
        Phone: values.phone,
        Message: values.message,
        Honeypot: values.honeypot,
      }),
    });

    //handle errors
    const data = await res.json();
    if (data.error) {
      setError(data.error.message);
    }

    SetSuccessMessage("Message Sent!");

    setTimeout(() => SetSuccessMessage("Send Message"), 3000);

    //clear form on submission
    reset();
  };

  return (
    <section className="my-40 md:mx-6  relative" id="contact">
      <div>
        <Star height={300} width={300} className="-left-16 -top-20" />
        <Star height={100} width={100} className="right-[300px] top-48" />
        <Star height={250} width={250} className="right-20 bottom-0" />
        <Star height={80} width={80} className="right-16 bottom-0" />
        <Star height={100} width={100} className="left-16 -bottom-20" />
      </div>
      <div className="flex justify-between gap-[10px] md:gap-[2vw] text-main-font max-w-[1600px] mx-auto">
        <form
          className="grid gap-10 w-[100vw] lg:w-[60vw] md:ml-6 px-6 md:px-20 py-16 bg-white-card rounded-3xl shadow-custom z-[2] border-2 border-main-font"
          onSubmit={handleSubmit(onSubmit)}
        >
          <h2 className="text-main-font mb-10 px-6 md:px-0">Get in touch</h2>
          <div className="flex flex-col">
            <label className="text-[14px] font-medium" htmlFor="firstName">
              First Name *
            </label>
            <input
              className="bg-transparent border-0 border-b-[1.5px] !border-mian-font focus:ring-0"
              id="firstName"
              name="firstName"
              {...register("firstName")}
            ></input>
            {errors.firstName && (
              <span className="text-red-900 text-sm">
                {errors.firstName.message}
              </span>
            )}
          </div>
          <div className="flex flex-col">
            <label className="text-[14px] font-medium" htmlFor="lastName">
              Last Name
            </label>
            <input
              className="bg-transparent border-0 border-b-[1.5px] !border-main-font focus:ring-0"
              id="lastName"
              name="lastName"
              {...register("lastName")}
            ></input>
            {errors.lastName && (
              <span className="text-red-900 text-sm">
                {errors.lastName.message}
              </span>
            )}
          </div>
          <div className="flex flex-col">
            <label className="text-[14px] font-medium" htmlFor="email">
              Email *
            </label>
            <input
              className="bg-transparent border-0 border-b-[1.5px] !border-main-font focus:ring-0"
              type="email"
              id="email"
              name="email"
              {...register("email")}
            ></input>
            {errors.email && (
              <span className="text-red-900 text-sm">
                {errors.email.message}
              </span>
            )}
          </div>
          <div className="flex flex-col">
            <label className="text-[14px] font-medium" htmlFor="phone">
              Phone Number
            </label>
            <input
              className="bg-transparent border-0 border-b-[1.5px] !border-main-font focus:ring-0"
              id="phone"
              name="phone"
              {...register("phone")}
            ></input>
            {errors.phone && (
              <span className="text-red-900 text-sm">
                {errors.phone.message}
              </span>
            )}
          </div>
          <div className="flex flex-col">
            <label className="text-[14px] font-medium" htmlFor="message">
              Message *
            </label>
            <textarea
              className="bg-transparent border-0 border-b-[1.5px] !border-main-font focus:ring-0 h-24"
              id="message"
              name="message"
              {...register("message")}
            ></textarea>
            {errors.message && (
              <span className="text-red-900 text-sm">
                {errors.message.message}
              </span>
            )}
          </div>

          <input
            type="hidden"
            name="honeypot"
            {...register("honeypot")}
          ></input>

          <button
            type="submit"
            className="bg-accent-color shadow-custom text-main-font font-medium py-4 rounded-lg mt-10 disabled:text-gray-600 hover:shadow-customHover duration-300"
            disabled={isSubmitting}
          >
            {SuccessMessage}
          </button>
        </form>

        {/* Contact Info Blocks */}
        <div className="hidden lg:flex flex-col items-center gap-12 pt-12 w-auto">
          <div className="rounded-3xl shadow-custom w-full p-10 bg-white-card z-[2] border-2 border-main-font">
            <h3 className="text-[24px] mb-3">Find us here</h3>
            <p className="text-[18px]">7915 Kensington Ct</p>
            <p className="text-[18px]">Brighton, MI 48116</p>
          </div>
          <div className="rounded-3xl shadow-custom w-full p-10 bg-white-card border-2 border-main-font">
            <h3 className="text-[24px] mb-3">Call us</h3>
            <p className="text-[16px]">(248)-446-0100</p>
          </div>
          <div className="rounded-3xl shadow-custom w-full p-10 bg-white-card z-[2] border-2 border-main-font">
            <h3 className="text-[24px] mb-3">Email us</h3>
            <ul className="text-[18px] flex flex-col gap-3">
              <li><EMAIL></li>
            </ul>
          </div>
        </div>
      </div>
    </section>
  );
}
