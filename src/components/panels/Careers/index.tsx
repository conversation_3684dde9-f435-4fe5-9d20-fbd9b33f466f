import { motion } from "framer-motion";
import { HomePagePayload } from "@/types";
import { SanityBlockRenderer } from "@/components/sanity-block-renderer";
import Star from "@/components/Star";
import { AnimatedHeading } from "@/components/ui/AnimatedHeader";
import Link from "next/link";

interface Props {
  careersSectionTitle: HomePagePayload["careersSectionTitle"];
  careersSectionDescription: HomePagePayload["careersSectionDescription"];
}

export default function Careers({
  careersSectionTitle,
  careersSectionDescription,
}: Props) {
  return (
    <section className="my-40 p-6 lg:pl-6 relative lg:mr-6" id="careers">
      <div>
        <Star height={100} width={100} className="-bottom-10 left-[800px]" />
        <Star height={300} width={300} className="bottom-10 -right-40" />
      </div>
      <div>
        <AnimatedHeading text={careersSectionTitle}></AnimatedHeading>
        <motion.div
          initial={{ y: 25, opacity: 0 }}
          whileInView={{ y: 0, opacity: 1 }}
          transition={{ duration: 0.5, delay: 0.3 }}
          viewport={{ once: true }}
          className="flex flex-col prose-lg prose-p:m-2 text-[18px] lg:max-w-[1000px] "
        >
          <SanityBlockRenderer
            value={careersSectionDescription}
          ></SanityBlockRenderer>
        </motion.div>
      </div>
      <div className="mt-12 flex flex-col items-center sm:flex-row justify-center gap-12 px-3">
        <Link
          href="https://www.linkedin.com/company/sundance-inc."
          target="_blank"
          className="font-medium text-[24px] bg-white-card text-main-font shadow-custom h-fit min-h-20 w-full md:w-[90%] md:max-w-[600px] py-4 rounded-xl hover:shadow-lydersHover duration-300 z-[2] border-2 hover:border-[#CC2ED5] border-main-font flex items-center justify-center"
        >
          <button>Taco Bell</button>
        </Link>
        <Link
          href="https://peopleandpetspharmacy.com/pages/career-opportunities"
          target="_blank"
          className="font-medium text-[24px] bg-white-card text-main-font shadow-custom h-fit min-h-20 md:h-20 w-full md:w-[90%] justify-center md:max-w-[600px] py-4 px-3 rounded-xl hover:shadow-pharmacyHover hover:border-[#10C4E3] duration-300 z-[2] border-2 border-main-font flex items-center"
        >
          <button>People & Pets Pharmacy</button>
        </Link>
      </div>
    </section>
  );
}
