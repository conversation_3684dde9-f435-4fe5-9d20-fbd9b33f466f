"use client";

import Hero from "@/components/panels/Hero";
import SideN<PERSON> from "@/components/SideNav";
import Story from "@/components/panels/Story";
import WhatWeDo from "@/components/panels/WhatWeDo";
import Values from "@/components/panels/Values";
import Contact from "@/components/panels/Contact";
import Careers from "@/components/panels/Careers";
import Footer from "@/components/panels/Footer";
import Header from "@/components/Header";
import Stats from "../../panels/Stats";
import Charities from "@/components/panels/Charities";
import type { HomePagePayload } from "@/types";

export interface HomePageProps {
  pageData: HomePagePayload | null;
}

export function HomePage({ pageData }: HomePageProps) {
  // Default to an empty object to allow previews on non-existent documents
  const page = pageData;

  if (!page) {
    return null;
  }

  return (
    <>
      <Header></Header>
      <Hero></Hero>
      <div className="flex">
        <SideNav></SideNav>
        <div className="w-[100%] lg:w-[90%] lg:pl-10 m-auto overflow-x-clip">
          <WhatWeDo></WhatWeDo>
          <Stats
            statsSectionTitle={page.statsSectionTitle}
            statsSectionDescription={page.statsSectionDescription}
          ></Stats>
          <Story></Story>
          <Values
            purposeStatement={page.purposeStatement}
            ambitionStatement={page.ambitionStatement}
          ></Values>
          <Charities></Charities>
          <Careers
            careersSectionTitle={page.careersSectionTitle}
            careersSectionDescription={page.careersSectionDescription}
          ></Careers>
          <Contact></Contact>
        </div>
      </div>

      <Footer></Footer>
    </>
  );
}
