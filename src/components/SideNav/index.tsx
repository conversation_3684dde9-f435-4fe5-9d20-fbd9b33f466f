import { useState, useEffect } from "react";
import Image from "next/image";
import Logo from "@/images/logo.svg";
import handleScroll from "../../utils/ScrollHandler";
import { motion } from "framer-motion";

const sections = [
  "whatWeDo",
  "story",
  "values",
  "charities",
  "careers",
  "contact",
];

export default function StickyNav() {
  const [activeSection, setActiveSection] = useState("");
  const [activeSectionIndex, setActiveSectionIndex] = useState(0);

  useEffect(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            const idName = entry.target.id;
            let index;
            switch (idName) {
              case "whatWeDo":
                index = 0;
                break;
              case "story":
                index = 1;
                break;
              case "values":
                index = 2;
                break;
              case "charities":
                index = 3;
                break;
              case "careers":
                index = 4;
                break;
              case "contact":
                index = 5;
                break;
            }
            setActiveSection(idName);
            setActiveSectionIndex(index);
          }
        });
      },
      { rootMargin: "-20% 0px -20% 0px", threshold: 0.1 } // Adjust rootMargin for triggering intersection later
    );

    // Start observing each section
    const observeSections = () => {
      sections.forEach((id) => {
        const element = document.getElementById(id);
        if (element) observer.observe(element);
      });
    };

    observeSections();

    // Cleanup observer
    return () => {
      observer.disconnect();
    };
  }, []);

  return (
    <motion.nav
      initial={{ y: -25, opacity: 0 }}
      whileInView={{ y: 0, opacity: 1 }}
      transition={{ duration: 1, delay: 0.5 }}
      viewport={{ amount: 0.5 }}
      className="hidden h-[100vh] lg:flex justify-center sticky top-0 mt-16"
    >
      <div className="w-36 flex flex-col text-[12px] pl-6">
        <Image src={Logo} className="h-40 w-40 my-2" alt="Lyders Group Logo" />
        <ul className="flex flex-col items-center">
          {sections.map((section, index) => (
            <li key={section} className="my-2 flex flex-col items-center">
              <a
                onClick={handleScroll}
                href={`#${section}`}
                className={`${
                  activeSection === section ? "opacity-100" : "opacity-50"
                } transition-opacity duration-300 mb-2`}
              >
                {section === "whatWeDo"
                  ? "What We Do"
                  : section === "story"
                    ? "Our Story"
                    : section === "values"
                      ? "Our Values"
                      : section === "charities"
                        ? "Charities"
                        : section === "careers"
                          ? "Careers"
                          : "Contact Us"}
              </a>

              {/* Show vertical line if this section is in view */}
              {index < sections.length - 1 && (
                <div
                  className={`vertical-line border-r border-accent-color transition-height duration-1000 ${
                    activeSectionIndex === index || activeSectionIndex > index
                      ? "h-10"
                      : "h-0"
                  }`}
                />
              )}
            </li>
          ))}
        </ul>
      </div>
    </motion.nav>
  );
}
