import Logo from "@/images/seal.svg";
import Image from "next/image";
import { useState, useEffect } from "react";
import { AnimatePresence, motion } from "framer-motion";
import "./Header.css";
import Star from "@/components/Star";

export default function Header() {
  const [navOpen, setNavOpen] = useState(false);

  function handleNav() {
    setNavOpen(!navOpen);
  }

  //stops the body elements from scrolling when full-page nav menu is open
  useEffect(() => {
    if (navOpen) {
      document.body.classList.add("overflow-hidden");
    } else {
      document.body.classList.remove("overflow-hidden");
    }
  }, [navOpen]);

  function handleClick() {
    setNavOpen(false);
  }

  return (
    <header
      className="lg:hidden flex items-center justify-between sticky top-0 bg-white-card py-2 px-10 z-10 border-b-4 border-main-font"
      id="header"
    >
      <a href="#hero">
        <Image src={Logo} className="w-[5vh] h-[5vh]" alt="Logo" />
      </a>

      <div className="flex align-center gap-2 pt-2">
        <button
          className="text-[18px] sm:text-[20px] font-headerFont font-medium"
          onClick={handleNav}
        >
          Menu
        </button>
      </div>
      <AnimatePresence>
        {navOpen && (
          <motion.nav
            initial={{ y: "-100%", opacity: 0 }}
            animate={{ y: 0, opacity: 1 }}
            transition={{ duration: 0.5 }}
            exit={{ y: "-100%", opacity: 0 }}
            className="fixed top-0 left-0 w-screen h-screen bg- text-main-font flex flex-col justify-center z-10 bg-main-font"
          >
            <button
              onClick={handleNav}
              className="fixed top-16 right-10 self-end text-[28px] bg-neutral-100 rounded-full px-6 font-bold"
            >
              &#10005;
            </button>
            <motion.ul
              initial={{ y: "100%", opacity: 0 }}
              animate={{ y: 0, opacity: 1 }}
              transition={{ delay: 0.3, duration: 0.5 }}
              exit={{ y: "100%", opacity: 0 }}
              className="grid gap-10 font-primaryFont text-[40px] p-3 text-center overflow-y-auto m-auto text-white-font"
            >
              <motion.li>
                <a onClick={handleClick} href="#story">
                  Our Story
                </a>
              </motion.li>
              <motion.li>
                <a onClick={handleClick} href="#whatWeDo">
                  What We Do
                </a>
              </motion.li>
              <motion.li>
                <a onClick={handleClick} href="#values">
                  Our Values
                </a>
              </motion.li>
              <motion.li>
                <a onClick={handleClick} href="#contact">
                  Contact Us
                </a>
              </motion.li>
              <motion.li>
                <a onClick={handleClick} href="#careers">
                  Careers
                </a>
              </motion.li>
            </motion.ul>
            <div className="pointer-events-none absolute top-0 left-0 w-[100%]">
              <div className="relative h-[100vh] w-[100vw]">
                <Star
                  height={50}
                  width={50}
                  className="top-20 left-32 !z-10 !opacity-100"
                />
                <Star
                  height={80}
                  width={80}
                  className="top-96 left-20 !z-10 !opacity-100"
                />
                <Star
                  height={40}
                  width={40}
                  className="top-[500px] left-10 !z-10 !opacity-100"
                />

                <Star
                  height={40}
                  width={40}
                  className="bottom-20 left-96 !z-10 !opacity-100"
                />

                <Star
                  height={80}
                  width={80}
                  className="top-20 right-40 !z-10 !opacity-100"
                />

                <Star
                  height={60}
                  width={60}
                  className="bottom-[300px] right-20 !z-10 !opacity-100"
                />
              </div>
            </div>
          </motion.nav>
        )}
      </AnimatePresence>
    </header>
  );
}
