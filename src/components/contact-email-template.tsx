import * as React from "react";

interface ContactEmailProps {
  FirstName: string;
  LastName: string;
  Email: string;
  Message: string;
  Phone: string;
}

const ContactEmail = (props: ContactEmailProps) => {
  const { FirstName, LastName, Email, Message, Phone } = props;
  const Name = `${FirstName} ${LastName}`;

  return (
    <div style={{ fontFamily: "system-ui, sans-serif", lineHeight: 1.5 }}>
      <h2>New Lyders Group contact form submission</h2>
      <p>
        <strong>Name:</strong> {Name}
      </p>
      <p>
        <strong>Email:</strong> {Email}
      </p>
      {Phone ?? (
        <p>
          <strong>Phone:</strong> {Phone}
        </p>
      )}
      <hr />
      <p>{Message}</p>
    </div>
  );
};

export default ContactEmail;
