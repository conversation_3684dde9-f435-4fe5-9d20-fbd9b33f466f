import Icons from "@/components/ui/svg/Icons";
import Styles from "./FeaturesGallery.module.scss";
import { useRef } from "react";

const Gallery = ({ children, className, scrollAmount = 300 }) => {
    const scrollContainerRef = useRef(null);
    const scrollLeft = () => {
        scrollContainerRef.current.scrollBy({ left: -scrollAmount, behavior: "smooth" });
    };
    const scrollRight = () => {
        scrollContainerRef.current.scrollBy({ left: scrollAmount, behavior: "smooth" });
    };

    return (
        <div className={`${Styles.galleryContainer} ${className}`}>
            <div ref={scrollContainerRef} className={Styles.scrollWrapper}>
                <div className={Styles.gallery}>{children}</div>
            </div>
            <div className={Styles.galleryActions}>
                <div className={Styles.actionsContainer}>
                    <div onClick={() => scrollLeft()} className={Styles.btnContainer}>
                        <Icons.ArrowRight style={{ transform: "rotate(180deg)" }} />
                    </div>
                    <div onClick={() => scrollRight()} className={Styles.btnContainer}>
                        <Icons.ArrowRight />
                    </div>
                </div>
            </div>
        </div>
    );
};
export default Gallery;
