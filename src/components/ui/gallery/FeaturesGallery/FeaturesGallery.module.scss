.galleryContainer {
    display: flex;
    flex-direction: column;
    align-items: center;
    position: relative;
    width: 100%;
    overflow: hidden;
    width: 100%;
    gap: 2rem;

    & .scrollWrapper {
        display: flex;
        scroll-behavior: smooth;
        flex: 1;
        -ms-overflow-style: none; /* IE and Edge */
        scrollbar-width: none; /* Firefox */
        width: 100%;
        border-radius: var(--border-radius);
        overflow-y: hidden;
        overflow-x: auto; // Enable horizontal scrolling
        scroll-snap-type: x mandatory; // Optional: For snap scrolling
        scroll-behavior: smooth; /* Smooth scrolling for non-touch devices */

        & .gallery {
            display: flex;
            gap: 2em;
        }
    }

    & .galleryActions {
        display: flex;
        align-items: center;
        justify-content: flex-end;
        padding-right: 6%;
        width: 100%;
        max-width: var(--max-width);

        .actionsContainer {
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 1.5em;

            & .btnContainer {
                display: flex;
                justify-content: center;
                align-items: center;
                height: 36px;
                width: 36px;
                background-color: rgba(210, 210, 215, 0.64);
                border-radius: 50%;
                cursor: pointer;

                transition: background-color 100ms linear, color 100ms linear, opacity 100ms linear;
            }
            & .btnContainer:hover {
                background-color: rgba(210, 210, 215, 0.386);
            }
        }
    }
}
