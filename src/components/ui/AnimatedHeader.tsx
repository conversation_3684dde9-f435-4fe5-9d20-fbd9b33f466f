import { motion } from "framer-motion";

export const AnimatedHeading = ({ text }: { text: string }) => {
  //split the text string into an array of words
  const words = text.split(" ");

  return (
    <h2 className="text-4xl md:text-5xl mb-5">
      {words.map((word, wi) => (
        <span key={wi} className="mr-2 inline-block whitespace-nowrap">
          {Array.from(word).map((char, i) => {
            const globalIndex =
              words.slice(0, wi).reduce((sum, w) => sum + w.length + 1, 0) + i;
            return (
              <motion.span
                key={i}
                className="inline-block"
                initial={{ y: 20, opacity: 0 }}
                whileInView={{ y: 0, opacity: 1 }}
                viewport={{ once: true }}
                transition={{
                  type: "spring",
                  stiffness: 300,
                  damping: 20,
                  delay: globalIndex * 0.03,
                }}
              >
                {char}
              </motion.span>
            );
          })}
        </span>
      ))}
    </h2>
  );
};
