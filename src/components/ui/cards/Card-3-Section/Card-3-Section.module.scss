.container {
    gap: 0.75rem;
    border-radius: var(--border-radius);
    flex-direction: column;
    padding: 2rem 1.25rem;
    display: flex;
    align-items: flex-start;
    position: relative;
    background-color: #e8eaed;
    color: var(--dark-grey);
    font-family: var(--Primary-Font);

    & .title {
        font-size: 1rem;
        font-weight: 600;
        letter-spacing: 0.1rem;
    }

    & .heading {
        font-size: 2rem;
        line-height: 1.25em;
    }
    & .content {
        letter-spacing: 0;
        font-size: 1rem;
        line-height: 1.25;
    }
}
