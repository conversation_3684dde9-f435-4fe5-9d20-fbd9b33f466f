import baseStyles from "./FeaturesCard.module.scss";
const FeaturesCard = ({ title, heading, copy, HeaderIcon, ButtonIcon, headerIconClass = "", buttonIconClass = "", className = "", buttonStyles = "", Styles, style }) => {
    return (
        <div className={`${baseStyles.card} ${className}`} style={style}>
            <div className={baseStyles.section1}>
                <div className={baseStyles.title}>
                    {HeaderIcon && <HeaderIcon className={headerIconClass} height={32} width={32} />}
                    <div className={baseStyles.copy}>
                        <h3>{title}</h3>
                    </div>
                </div>
                <div className={baseStyles.content}>
                    <div className={baseStyles.copy}>
                        <h2>{heading}</h2>
                    </div>
                </div>
            </div>
            <div className={baseStyles.section2}>
                <div className={baseStyles.textContainer}>
                    <div className={baseStyles.copy}>
                        <p>{copy}</p>
                    </div>
                </div>
            </div>
        </div>
    );
};

export default FeaturesCard;
