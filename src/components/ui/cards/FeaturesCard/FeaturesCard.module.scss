.card {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: flex-start;
    background-color: #fff;
    padding: 2rem 1.5rem;
    border-radius: var(--border-radius);
    flex: 0 0 275px;
    gap: 1rem;
    font-family: var(--Primary-Font);
    line-height: 1.25;
    background-color: var(--paper-color);
    color: var(--dark-grey);
    scroll-snap-align: start;

    & h2 {
        font-size: 2.25rem;
        font-weight: 500;
        font-family: var(--Primary-Font);
    }
    & h3 {
        font-size: 1.25rem;
        font-weight: 500;
    }
    & p {
        font-size: 1rem;
        font-weight: 400;
        line-height: 1.25;
    }

    & .section1 {
        display: flex;
        flex-direction: column;
        gap: 1rem;
        height: 100%;

        & .title {
            display: flex;
            width: 100%;
            align-items: center;
            justify-content: flex-start;
        }

        & .title > svg {
            margin-right: 6px;
        }

        & .content {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            width: 100%;
        }
    }
    & .section2 {
        height: 100%;

        & .textContainer {
            display: flex;
        }
    }
}
.card {
    transition: transform 0.3s cubic-bezier(0, 0, 0.5, 1);
    transition-behavior: normal;
    transition-duration: 0.3s;
    transition-timing-function: cubic-bezier(0, 0, 0.5, 1);
    transition-delay: 0s;
    transition-property: transform;
}
// .card:hover {
//     transform: scale(1.016);
// }
@media (max-width: 768px) {
    .card {
        flex: 0 0 225px;

        & h2 {
            font-size: 1.75rem;
        }
        & h3 {
            font-size: 1.25rem;
        }
        & p {
            font-size: 0.9rem;
        }
    }
}
