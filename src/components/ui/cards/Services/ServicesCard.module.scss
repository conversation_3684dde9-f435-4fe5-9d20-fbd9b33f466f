.card {
    background-position: center;
    background-size: cover;
    background-repeat: no-repeat;
    display: flex;
    flex-direction: column;
    padding: 2rem;
    gap: 1.5rem;
    color: var(--paper-color);
    color: rgba(255, 255, 255, 0.82);
    font-family: var(--Primary-Font);
    border-radius: var(--border-radius);
    position: relative;
    cursor: pointer;
    align-items: flex-start;
    justify-content: flex-start;
    flex: 1 1 100px;

    .overlay {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: linear-gradient(to bottom, rgba(0, 0, 0, 0.65), rgba(0, 0, 0, 0.1)); /* Gradient overlay */
        border-radius: var(--border-radius);
        cursor: pointer;
        pointer-events: none;
        display: flex;
        z-index: 1;
    }

    .title {
        font-size: 1.2rem;
        font-size: 1.3rem;
        z-index: 2;
        font-weight: 500;
        flex: 1 1 auto;
    }
    .content {
        font-size: 2rem;
        font-size: 2.25rem;
        font-weight: 600;
        z-index: 2;
        flex: 1 1 auto;
    }
}

.actionButton {
    display: flex;
    align-items: center;
    justify-content: flex-start;
    position: relative;
    z-index: 3;
    button {
        font-size: 1rem;
        padding: 0.75rem 1rem;
        background-color: var(--theme-purple);
        color: #fff;
    }
}
