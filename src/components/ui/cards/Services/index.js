import baseStyles from "./ServicesCard.module.scss";

const ServicesCard = ({ backgroundImage = "", title, content, onClick }) => {
    return (
        <div className={baseStyles.card} onClick={onClick} style={{ backgroundImage: `url(${backgroundImage})` }}>
            <div className={baseStyles.overlay}></div>
            <div className={baseStyles.title}>{title}</div>
            <div className={baseStyles.content}>{content}</div>
            <div className={baseStyles.actions}>
                <div className={baseStyles.actionButton}>
                    <button className={`TLButton`}>Learn More</button>
                </div>
            </div>
        </div>
    );
};

export default ServicesCard;
