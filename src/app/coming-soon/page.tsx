import "./coming-soon.css";
import Facebook from "../../../public/images/facebook-blue.png";
import Instagram from "../../../public/images/instagram-green.png";
import LinkedIn from "../../../public/images/linkedin-red.png";
import Image from "next/image";

export default function ComingSoon() {
  return (
    <section id="coming-soon" className="flex flex-col justify-center h-screen">
      <div>
        <div className="marquee-wrapper">
          <div className="marquee marquee1">
            <h2 className="text-[64px] md:text-[104px]">
              Lyders Group - Lyders Group - Lyders Group - Lyders Group - Lyders
              Group -&nbsp;
            </h2>
            <h2 className="text-[64px] md:text-[104px]">
              Lyders Group - Lyders Group - Lyders Group - Lyders Group - Lyders
              Group -
            </h2>
          </div>
        </div>
        <div className="marquee-wrapper">
          <div className="marquee marquee2">
            <p className="text-[44px] md:text-[74px]">
              Food&nbsp; Technology&nbsp; Medical&nbsp; Scientific&nbsp;
              Hardware&nbsp; Aviation&nbsp; Farm&nbsp;&nbsp;
            </p>
            <p className="text-[44px] md:text-[74px]">
              Food&nbsp; Technology&nbsp; Medical&nbsp; Scientific&nbsp;
              Hardware&nbsp; Aviation&nbsp; Farm
            </p>
          </div>
        </div>
        <div className="marquee-wrapper">
          <div className="marquee marquee3">
            <h2 className="text-[64px] md:text-[104px]">
              <i>
                Coming Soon - Coming Soon - Coming Soon - Coming Soon - Coming
                Soon - Coming Soon -&nbsp;
              </i>
            </h2>
            <h2 className="text-[64px] md:text-[104px]">
              <i>
                Coming Soon - Coming Soon - Coming Soon - Coming Soon - Coming
                Soon - Coming Soon -;
              </i>
            </h2>
          </div>
        </div>
      </div>
      <div className="flex self-center mt-10 gap-10">
        <a
          href="https://www.linkedin.com/company/sundance-inc."
          target="_blank"
        >
          <Image
            src={LinkedIn}
            alt="linkedin"
            className="h-10 w-10 sm:h-16 sm:w-16"
          ></Image>
        </a>
        <a href="https://www.facebook.com/Teamlyders/" target="_blank">
          <Image
            src={Facebook}
            alt="facebook"
            className="h-10 w-10 sm:h-16 sm:w-16"
          ></Image>
        </a>
        <a href="https://www.instagram.com/teamlyders/?hl=en" target="_blank">
          <Image
            src={Instagram}
            alt="instagram"
            className="h-10 w-10 sm:h-16 sm:w-16"
          ></Image>
        </a>
      </div>
    </section>
  );
}
