@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  /* --Background-Color: #efe4db; */
  --Background-Color: #ffeedc;
  /* --Black-Font: #2e1b0e; */
  --Black-Font: #0a2749;
  --White-Font: var(--Background-Color);
  --White-Card: #fff8f2;
  /* --Red-Color: #bc5656; */
  --Accent-Color: #edaf54;
}

* {
  box-sizing: border-box;
}

body {
  font-family: var(--Primary-Font);
  color: var(--Black-Font);
  background-color: var(--Background-Color);
  height: 100vh;
  overflow-x: hidden;
  background-color: var(--Background-Color);
  background-image: url("https://www.transparenttextures.com/patterns/natural-paper.png");
  /* background-image: url("../images/watercolor.jpg"); */
  /* background-size: cover; */
  /* background-repeat: no-repeat; */
}

#hero {
  /* background-image: url("https://www.transparenttextures.com/patterns/natural-paper.png"); */
  background-color: var(--Background-Color);
  background-size: cover;
  /* background-position: center; */

  background-image: linear-gradient(
      to bottom,
      rgba(255, 255, 255, 0) 70%,
      var(--Background-Color) 100%
    ),
    url("../images/watercolor-bg-7.png"),
    url("https://www.transparenttextures.com/patterns/natural-paper.png");
}

.tech {
  min-width: 250px;
  width: 23vw;
  height: auto;
  left: 15%;
  top: 6%;
  position: absolute;
  max-width: 320px;
}

.aviation {
  right: 10%;
  top: 8%;
  position: absolute;
  width: 45vw;
  height: auto;
  rotate: -5deg;
  min-width: 450px;
  max-width: 600px;
  z-index: 0;
}

.hardware {
  position: absolute;
  width: 8vw;
  height: auto;
  min-width: 80px;
  max-width: 100px;
  top: 32%;
  left: 12%;
  rotate: -40deg;
}

.medical {
  position: absolute;
  width: 10vw;
  height: auto;
  top: 55%;
  left: 10%;
  max-width: 150px;
  min-width: 100px;
}

.science {
  position: absolute;
  width: 14vw;
  height: auto;
  top: 38%;
  right: 6%;
  max-width: 200px;
}

.farm {
  position: absolute;
  width: 22vw;
  height: auto;
  max-width: 380px;
  min-width: 300px;
  bottom: -2%;
  left: 18%;
}

.food {
  width: 30vw;
  height: auto;
  position: absolute;
  bottom: 0;
  right: 15%;
  max-width: 500px;
  min-width: 400px;
  bottom: -3%;
}

/* #hero-copy {
  bottom: 150px;
} */

@media (max-width: 1100px) {
  .aviation {
    right: 5%;
  }

  .tech {
    left: 12%;
  }

  .farm {
    left: 8%;
  }

  .food {
    right: 8%;
  }
}

@media (max-width: 1024px) {
  .aviation {
    width: 58vw;
    top: 5%;
    right: 2%;
  }

  .tech {
    width: 28vw;
    left: 5%;
    top: 3%;
  }

  .hardware {
    top: 30%;
  }

  .science {
    top: 32%;
    width: 16vw;
  }

  .hardware {
    top: 30%;
    left: 15%;
  }

  .medical {
    width: 20vw;
    top: 42%;
    left: 5%;
  }

  .food {
    bottom: 5%;
    right: 5%;
  }

  .farm {
    bottom: 5%;
  }
}

@media (max-width: 900px) {
  .aviation {
    rotate: 0deg;
  }
  .hardware {
    top: 28%;
  }
  .medical {
    top: 38%;
    left: 3%;
  }
}

@media (max-width: 800px) {
  .tech {
    top: 7%;
    left: 1%;
  }
  .aviation {
    top: 2%;
    width: 62vw;
  }

  .hardware {
    top: 18%;
    left: 45%;

    min-width: 75px;
  }

  .medical {
    top: 32%;
    left: 8%;
    width: 16vw;
  }

  .farm {
    bottom: 18%;
    min-width: 200px;
    left: 3%;
  }

  .food {
    bottom: 10%;
  }

  .science {
    top: 28%;
    width: 18vw;
  }
}

@media (max-width: 650px) {
  .food {
    bottom: 20%;
    min-width: 300px;
    right: 2%;
  }

  .hardware {
    top: 58%;
    left: 30%;
    rotate: -50deg;
  }

  .tech {
    top: 10%;
  }

  .aviation {
    top: 0;
    min-width: 200px;
    right: 5%;
  }

  .science {
    top: 22%;
  }

  .medical {
    top: 18%;
    left: 50%;
  }

  #hero-copy {
    bottom: 80px;
  }
}

@media (max-width: 550px) {
  .hardware {
    top: 57%;
    left: 12%;
    rotate: -30deg;
  }

  .food {
    bottom: 20%;
  }

  .tech {
    min-width: 180px;
    top: 12%;
  }

  .medical {
    top: 18%;
    left: 45%;
  }

  .science {
    min-width: 120px;
    top: 10%;
  }

  .aviation {
    right: 20%;
    top: 2%;
  }

  .farm {
    left: 2%;
    bottom: 8%;
  }
}

@media (max-width: 450px) {
  .aviation {
    min-width: 380px;
    left: 3%;
    top: 0;
  }

  .hardware {
    rotate: 10deg;
    top: 60%;
    left: 11%;
  }

  .tech {
    min-width: 170px;
    top: 15%;
    left: 5%;
  }

  .medical {
    top: 68%;
    left: 35%;
    min-width: 120px;
  }

  .food {
    bottom: -1%;
    right: 8%;
    min-width: 380px;
  }

  .farm {
    bottom: 28%;
    left: 50%;
  }
  .science {
    top: 15%;
    right: 10%;
  }
}

@media (max-width: 420px) {
  .food {
    right: 5%;
    bottom: -2%;
  }
  .farm {
    left: 45%;
    bottom: 28%;
  }

  .hardware {
    top: 58%;
  }

  .medical {
    left: 32%;
    top: 68%;
  }

  .aviation {
    left: 2%;
    top: 1%;
  }
  .science {
    top: 15%;
  }
  .tech {
    top: 15%;
  }
}

@media (max-width: 375px) {
  .aviation {
    left: 2%;
    min-width: 350px;
    top: 1%;
  }

  .medical {
    top: 70%;
    left: 30%;
  }

  .hardware {
    left: 8%;
  }

  .farm {
    left: 40%;
    bottom: 28%;
  }

  .food {
    min-width: 350px;
    right: 3%;
  }
  .science {
    top: 15%;
  }
  .tech {
    top: 15%;
  }
}

/* IPAD PRO */
@media (width: 1024px) and (height: 1366px) {
  .medical {
    left: 42%;
    min-width: 180px;
    top: 22%;
  }

  .hardware {
    top: 26%;
    rotate: -35deg;
    left: 12%;
    min-width: 120px;
  }

  .food {
    min-width: 550px;
    bottom: 6%;
    right: 4%;
  }

  .farm {
    min-width: 350px;
    left: 4%;
    bottom: 10%;
  }

  .science {
    min-width: 180px;
    top: 28%;
  }

  #hero-copy {
    bottom: 350px;
  }
}

/* IPAD AIR */
@media (width: 820px) and (height: 1180px) {
  .medical {
    left: 40%;
    min-width: 170px;
    top: 17%;
  }

  /* .tech {
    top: 20%;
    right: 2%;
  } */

  .aviation {
    min-width: 500px;
    top: 25;
    rotate: 0deg;
  }

  .hardware {
    top: 25%;
    rotate: -35deg;
    left: 10%;
    min-width: 100px;
  }

  .food {
    min-width: 450px;
    bottom: 6%;
  }

  .farm {
    min-width: 300px;
    left: 2%;
    bottom: 10%;
  }
  .science {
    top: 25%;
    min-width: 170px;
  }
}

p {
  font-weight: 400;
  line-height: 28px;
  opacity: 0.9;
}

h1,
h2,
h3,
span {
  font-family: var(--Header-Font);
  font-weight: 400;
}

h2 {
  font-size: 48px;
}

h2 {
  position: relative;
  z-index: 1;
}

h1,
h2,
h3 {
  z-index: 1;
}

.star {
  opacity: 0.3;
}

/* adds shadow to left side of timeline and dna card container */
.timeline-wrapper {
  mask: linear-gradient(90deg, transparent, var(--Background-Color) 5%);
}
#dnaCardContainer {
  mask: linear-gradient(90deg, transparent, var(--Background-Color) 2%);
}

/* CUSTOM SCROLLBAR */
::-webkit-scrollbar {
  display: block;
  height: 8px;
  width: 10px;
  border-radius: 20px;
}

::-webkit-scrollbar-track {
  background: transparent;
}

::-webkit-scrollbar-thumb {
  background-color: #3f3f3f;
  border-radius: 20px;
  border-right: none;
  border-left: none;
}

::-webkit-scrollbar-thumb:hover {
  background-color: #3f3f3f;
}

::-webkit-scrollbar-track-piece:end {
  background: rgba(63, 63, 63, 0.2);
  border-radius: 20px;
  margin-bottom: 5px;
}

::-webkit-scrollbar-track-piece:start {
  background: rgba(63, 63, 63, 0.2);
  border-radius: 20px;
  margin-top: 5px;
  margin-left: 24px;
}

/* Adjust scrollbar width for values section */
#values ::-webkit-scrollbar {
  width: 10px;
}

/* Handles max size for Lyders Group Hero font */
@media screen and (min-width: 1600px) {
  #hero-logo {
    font-size: 272px;
  }
}

/* remove timeline fade effect on smaller screens */
@media screen and (max-width: 1023px) {
  .timeline-wrapper,
  .dnaContainer {
    mask: none;
  }
}
