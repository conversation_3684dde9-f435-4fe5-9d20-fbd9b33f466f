import localFont from "next/font/local";
import { Suspense } from "react";
import GoogleAnalytics from "./GoogleAnalytics";

import "./globals.css";

const larish_neue_semibold = localFont({
  src: "../../public/fonts/Larish_Neue/larish_neue_semibold.otf",
  variable: "--Header-Font",
});

const montserrat = localFont({
  src: [
    {
      path: "../../public/fonts/Montserrat/Montserrat-Bold.woff",
      weight: "700",
      style: "normal",
    },
    {
      path: "../../public/fonts/Montserrat/Montserrat-ExtraLight.woff",
      weight: "200",
      style: "normal",
    },
    {
      path: "../../public/fonts/Montserrat/Montserrat-Light.woff",
      weight: "300",
      style: "normal",
    },
    {
      path: "../../public/fonts/Montserrat/Montserrat-Medium.woff",
      weight: "500",
      style: "normal",
    },
    {
      path: "../../public/fonts/Montserrat/Montserrat-Regular.woff",
      weight: "400",
      style: "normal",
    },
    {
      path: "../../public/fonts/Montserrat/Montserrat-SemiBold.woff",
      weight: "600",
      style: "normal",
    },
    {
      path: "../../public/fonts/Montserrat/Montserrat-Thin.woff",
      weight: "100",
      style: "normal",
    },
  ],
  variable: "--Primary-Font",
});

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html
      lang="en"
      className={`${larish_neue_semibold.variable} ${montserrat.variable}`}
    >
      <body>
        <Suspense fallback={null}>
          <GoogleAnalytics />
        </Suspense>
        <main>{children}</main>
      </body>
    </html>
  );
}
