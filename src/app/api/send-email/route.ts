import { NextResponse } from "next/server";
import { Resend } from "resend";
import ContactEmail from "../../../components/contact-email-template";

const resend = new Resend(process.env.RESEND_API_KEY!);

export async function POST(request: Request) {
  const { FirstName, LastName, Email, Phone, Message } = await request.json();
  const EmailTemplate = ContactEmail({
    FirstName,
    LastName,
    Email,
    Phone,
    Message,
  });
  try {
    await resend.emails.send({
      to: "<EMAIL>",
      from: "<EMAIL>",
      subject: `New Lyders Group Contact Submission`,
      react: EmailTemplate,
      replyTo: Email,
    });
    return NextResponse.json({ message: "Em<PERSON> sent!" });
  } catch (err: any) {
    return NextResponse.json(
      { message: "Error sending email", error: err.message },
      { status: 500 }
    );
  }
}
