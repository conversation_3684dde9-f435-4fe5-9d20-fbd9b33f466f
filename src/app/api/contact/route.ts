import { env } from "@/env";
import { NextRequest } from "next/server";

export async function POST(request: NextRequest) {
  const key = env.TL_API_KEY;

  //parse incoming data
  const body = JSON.stringify(await request.json());

  //make POST request to contact form api
  const results = await fetch(
    "http://api.teamlyders.co/?Client=LydersGroup&Connection=ContactForm",
    {
      method: "POST",
      headers: {
        token: key,
        "Content-Type": "application/json",
      },
      body,
    }
  );

  console.log("Fetched status:", results.status);
  console.log("Content-Type:", results.headers.get("content-type"));

  //process response from api
  const emailResult = await results.json();

  //handle errors
  if (emailResult.Error) {
    return Response.json(
      {
        message: "failed",
        error: "Sorry, your message couldnt be sent, please retry",
      },
      { status: 400 }
    );
  }
  return Response.json({ message: "success", error: "" }, { status: 200 });
}
