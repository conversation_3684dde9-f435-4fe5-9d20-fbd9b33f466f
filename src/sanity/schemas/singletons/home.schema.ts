import { IconHome } from "@tabler/icons-react";
import { defineArrayMember, defineField, defineType } from "sanity";

export const homeSchema = defineType({
  name: "homePage",
  title: "Home Page",
  type: "document",
  icon: IconHome,
  // Uncomment below to have edits publish automatically as you type
  // liveEdit: true,
  groups: [
    {
      name: "seo",
      title: "SEO",
    },
    {
      name: "heroSection",
      title: "Hero Section",
    },
    {
      name: "whatWeDoSection",
      title: "What We Do Section",
    },
    {
      name: "statsSection",
      title: "Stats Section",
    },
    {
      name: "storySection",
      title: "Story Section",
    },
    {
      name: "valuesSection",
      title: "Values Section",
    },
    {
      name: "careersSection",
      title: "Careers Section",
    },
  ],
  fields: [
    defineField({
      name: "seoTitle",
      title: "SEO title",
      type: "string",
      group: "seo",
    }),
    defineField({
      name: "seoDescription",
      title: "SEO description",
      type: "string",
      group: "seo",
    }),
    defineField({
      name: "seoImage",
      title: "Image",
      type: "image",
      group: "seo",
    }),

    // Hero Section
    defineField({
      name: "heroSectionTitle",
      title: "Hero Section Title",
      type: "string",
      validation: (Rule) => Rule.required(),
      group: "heroSection",
    }),
    defineField({
      name: "heroSectionDescription",
      title: "Hero Section Description",
      type: "string",
      group: "heroSection",
    }),

    // What We Do Section
    defineField({
      name: "whatWeDoTitle",
      title: "What We Do Title",
      type: "string",
      validation: (Rule) => Rule.required(),
      group: "whatWeDoSection",
    }),
    defineField({
      name: "whatWeDoDescription",
      title: "What We Do Description",
      type: "array",
      of: [
        {
          type: "block",
        },
      ],
      group: "whatWeDoSection",
    }),
    defineField({
      name: "whatWeDoCards",
      title: "What We Do Cards",
      type: "array",
      of: [
        defineArrayMember({
          type: "object",
          name: "inline",
          fields: [
            { type: "string", name: "segmentName" },
            { type: "string", name: "establishedYear" },
            { type: "text", name: "description" },
            { type: "image", name: "segmentImage" },
            { type: "string", name: "cardColor" },
          ],
        }),
      ],
      group: "whatWeDoSection",
    }),
    defineField({
      name: "whatWeDoBrands",
      title: "What We Do Brands",
      type: "array",
      of: [
        defineArrayMember({
          type: "object",
          name: "inline",
          fields: [
            { type: "string", name: "segment" },
            { type: "url", name: "websiteURL" },
            { type: "image", name: "brandImage" },
          ],
        }),
      ],
      group: "whatWeDoSection",
    }),

    // Stats Section
    defineField({
      name: "statsSectionTitle",
      title: "Stats Section Title",
      type: "string",
      validation: (Rule) => Rule.required(),
      group: "statsSection",
    }),
    defineField({
      name: "statsSectionDescription",
      title: "Stats Section Description",
      type: "array",
      of: [
        {
          type: "block",
        },
      ],
      group: "statsSection",
    }),

    //Story Section
    defineField({
      name: "storySectionTitle",
      title: "Story Section Title",
      type: "string",
      validation: (Rule) => Rule.required(),
      group: "storySection",
    }),
    defineField({
      name: "storySectionDescription",
      title: "Story Section Description",
      type: "text",
      group: "storySection",
    }),

    //Values Section
    defineField({
      name: "purposeStatement",
      title: "Purpose Statement",
      type: "text",
      group: "valuesSection",
    }),
    defineField({
      name: "ambitionStatement",
      title: "Ambition Statement",
      type: "text",
      group: "valuesSection",
    }),

    // Careers Section
    defineField({
      name: "careersSectionTitle",
      title: "Careers Section Title",
      type: "string",
      validation: (Rule) => Rule.required(),
      group: "careersSection",
    }),
    defineField({
      name: "careersSectionDescription",
      title: "Careers Section Description",
      type: "array",
      of: [
        {
          type: "block",
        },
      ],
      group: "careersSection",
    }),
  ],

  preview: {
    select: {
      title: "seoTitle",
    },
    prepare({ title }) {
      return {
        subtitle: "Home Page",
        title,
      };
    },
  },
});
