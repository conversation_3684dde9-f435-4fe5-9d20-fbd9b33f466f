import type { Config } from "tailwindcss";

export default {
  content: [
    "./app/**/*.{js,ts,jsx,tsx,mdx}",
    "./pages/**/*.{js,ts,jsx,tsx,mdx}",
    "./components/**/*.{js,ts,jsx,tsx,mdx}",

    // Or if using `src` directory:
    "./src/**/*.{js,ts,jsx,tsx,mdx}",
  ],
  theme: {
    extend: {
      colors: {
        "main-font": "var(--Black-Font)",
        "accent-color": "var(--Accent-Color)",
        "white-font": "var(--White-Font)",
        "white-card": "var(--White-Card)",
        "background-color": "var(--Background-Color)",
      },
      fontFamily: {
        headerFont: "var(--Header-Font)",
        primaryFont: "var(--Primary-Font)",
      },
      boxShadow: {
        custom: "5px 5px 0 #0a2749",
        customHover: "10px 10px 0 #0a2749",
        lyders: "5px 5px 0 #CC2ED5",
        lydersHover: "10px 10px 0 #CC2ED5",
        pharmacy: "5px 5px 0 #10C4E3",
        pharmacyHover: "10px 10px 0 #10C4E3",
        white: "5px 5px 0 #FFF8F2",
        imageShadow: "0 0 8px 8px #FFEEDC",
      },
    },
  },
  plugins: [require("@tailwindcss/typography"), require("@tailwindcss/forms")],
} satisfies Config;
